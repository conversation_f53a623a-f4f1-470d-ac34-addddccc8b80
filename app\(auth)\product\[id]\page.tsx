import PageHeader from "@/components/general/PageHeader";
import { Button } from "@/components/ui/button";
import PageWrapper from "@/components/wrappers/authPageWrapper";
import { Edit, TrashIcon } from "lucide-react";
import React, { use } from "react";

interface ProductDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function ProductDetailPage({ params }: ProductDetailPageProps) {
  const { id } = use(params);
  console.log(id);
  return (
    <PageWrapper>
      <PageHeader
        title="Szczegóły produktu"
        actions={
          <>
            <Button size={"icon"} variant={"outline"}>
              <Edit className="w-4 h-4" />
            </Button>
            <Button size={"icon"} variant={"outline"} className="text-destructive hover:text-destructive">
              <TrashIcon className="w-4 h-4" />
            </Button>
          </>
        }
      />
    </PageWrapper>
  );
}
