"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface ProductDetailDefaultMeasurementCardProps {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  unit: string;
  className?: string;
}

export function ProductDetailDefaultMeasurementCard({
  calories,
  protein,
  carbs,
  fat,
  unit,
  className,
}: ProductDetailDefaultMeasurementCardProps) {
  return (
    <div
      className={cn(
        "rounded-lg border bg-card p-6 shadow-sm",
        className
      )}
    >
      {/* Header */}
      <h2 className="text-xl font-semibold text-foreground mb-6">
        Wartości odżywcze (100{unit})
      </h2>

      {/* Nutrition information */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <span className="text-base text-foreground">Kalorie</span>
          <span className="text-base font-medium text-foreground">
            {calories} kcal
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-base text-foreground">Białko</span>
          <span className="text-base font-medium text-foreground">
            {protein}g
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-base text-foreground">Węglowodany</span>
          <span className="text-base font-medium text-foreground">
            {carbs}g
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-base text-foreground">Tłuszcze</span>
          <span className="text-base font-medium text-foreground">
            {fat}g
          </span>
        </div>
      </div>
    </div>
  );
}

export type { ProductDetailDefaultMeasurementCardProps };